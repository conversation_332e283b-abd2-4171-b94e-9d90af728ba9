# AutoJS6 自动化脚本集合

基于 AutoJS6 的自动化脚本集合，包含QQ自动回赞和番茄小说自动播放等实用工具。

## 🚀 脚本列表

### 📱 QQ自动回赞脚本 v3.0
自动为QQ好友点赞，支持多种解锁方式和智能配置管理。

**主要特性：**
- 🔧 配置外部化，敏感信息安全存储
- 🔓 支持三种解锁方式（无密码/手势/数字密码）
- 📱 智能适配不同屏幕尺寸
- 🔄 自动重试机制，提高成功率
- 📊 详细日志和统计信息

### 📚 番茄小说自动播放脚本
自动执行番茄小说的看广告领时长任务，支持静音和自动续播模式。

**主要特性：**
- 🎯 自动点击"开宝箱领时长"
- 📺 智能处理广告播放
- 🔇 支持静音模式和自动续播
- 🔄 循环执行，自动统计成功率
- ⏹️ 悬浮停止按钮，随时终止

## 📁 项目结构

```
├── QQ自动回赞3.0.js      # QQ回赞主脚本
├── 番茄小说.js           # 番茄小说自动播放脚本
├── 解锁配置助手.js       # 解锁配置辅助工具
├── QQConfig.def.json     # QQ配置模板（安全）
├── QQConfig.json        # QQ实际配置（敏感，已忽略）
├── .gitignore           # Git忽略规则
└── README.md            # 项目说明
```

## 🚀 快速开始

### 环境要求
- Android 7.0+ 系统
- AutoJS6 最新版本
- 已开启无障碍服务权限

## 📱 QQ自动回赞使用指南

### 1. 首次配置
1. 运行 `QQ自动回赞3.0.js`
2. 脚本会自动创建配置文件 `QQConfig.json`
3. 编辑配置文件，修改你的QQ号：
   ```json
   {
     "app": {
       "myQQ": "你的QQ号"
     }
   }
   ```

### 2. 解锁方式配置

#### 方法一：使用配置助手（推荐）
运行 `解锁配置助手.js`，通过图形界面轻松配置：
- 选择解锁类型
- 引导式坐标录入
- 配置验证测试

#### 方法二：手动配置
根据你的解锁方式选择：

**无密码解锁**
```json
{"unlock": {"type": "none"}}
```

**手势密码**
1. 开启"开发者选项" → "指针位置"
2. 记录解锁图案各点坐标
3. 配置 `gesturePath` 数组

**数字密码**
1. 记录数字键盘0-9的坐标
2. 配置密码和键盘布局

### 3. 运行脚本
配置完成后直接运行 `QQ自动回赞3.0.js` 即可。

## 📚 番茄小说自动播放使用指南

### 1. 功能说明
自动执行番茄小说的"开宝箱领时长"任务，通过看广告获得免费阅读时长。

### 2. 使用步骤
1. 打开番茄小说APP
2. 进入有"开宝箱领时长"按钮的页面
3. 运行 `番茄小说.js` 脚本
4. 选择运行模式：
   - **静音模式**：自动静音播放广告
   - **自动续播**：静音 + 自动播放下一个广告

### 3. 脚本特性
- 🎯 自动识别并点击"开宝箱领时长"
- 📺 智能处理"看视频最高再得XX分钟"按钮
- 🔇 自动静音广告播放
- ⏱️ 智能等待广告倒计时结束
- 🔄 循环执行，统计成功率
- ⏹️ 悬浮停止按钮，随时终止

### 4. 注意事项
- 需要Shizuku权限用于静音操作
- 建议在WiFi环境下使用
- 脚本会自动处理广告加载失败的情况

## ⚙️ QQ脚本配置说明

### 基础配置
```json
{
  "app": {
    "myQQ": "你的QQ号",        // 必须修改
    "clickCounts": 25,         // 每轮点赞数量
    "loadMoreTime": 0          // 加载更多次数，0=无限制
  },
  "timing": {
    "refreshDelay": 2000,      // 刷新等待时间
    "scrollDelay": 1000,       // 滑动等待时间
    "waitTimeout": 5000        // 元素等待超时
  }
}
```

## 🔒 安全与隐私

### 敏感信息保护
- 🛡️ QQ号、解锁密码等敏感信息仅存储在本地
- 🔐 配置文件自动加入 `.gitignore`，不会被提交
- 📁 只有配置模板会被版本控制，实际配置完全本地化

### 版本控制安全
```bash
# 安全文件（会被提交）
✅ 脚本文件 (.js)
✅ 配置模板 (.def.json)
✅ 说明文档

# 敏感文件（自动忽略）
❌ QQConfig.json
❌ *.log
❌ 个人配置文件
```

## 🛠️ 故障排除

### QQ脚本常见问题
| 问题 | 解决方案 |
|------|----------|
| 配置文件错误 | 检查JSON格式，使用配置助手 |
| 解锁失败 | 重新获取坐标，检查解锁类型 |
| 点赞无效 | 确认QQ号正确，检查无障碍权限 |
| 找不到按钮 | 确保QQ版本兼容，检查界面变化 |

### 番茄小说常见问题
| 问题 | 解决方案 |
|------|----------|
| 无法静音 | 检查Shizuku权限，确保服务运行 |
| 广告加载失败 | 检查网络连接，重启APP |
| 按钮识别失败 | 确保APP版本兼容 |
| 脚本卡住 | 使用悬浮停止按钮终止 |

## 📈 更新日志

### v3.1 (最新)
- 🔓 支持三种解锁方式（无密码/手势/数字密码）
- 🛠️ 新增解锁配置助手工具
- 📚 添加番茄小说自动播放脚本
- 📝 重构文档，合并说明文件

### v3.0
- 🔧 配置文件外部化
- 🛡️ 敏感信息保护
- 📱 智能坐标适配
- 📊 详细日志统计

## ⚠️ 使用须知

### 合规使用
- 请遵守相关APP的使用条款
- 适度使用，避免频繁操作
- 不要用于商业用途

### 隐私安全
- 配置文件包含敏感信息，请勿分享
- 定期备份配置（注意隐私保护）
- 使用最新版本AutoJS6

### 技术支持
- 确保无障碍服务权限已开启
- 检查APP版本兼容性
- 查看控制台日志排查问题

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目：
- 🐛 报告Bug和问题
- 💡 提出新功能建议
- 📝 改进文档说明
- 🔧 提交代码优化

**注意**: 提交时请勿包含个人配置文件

## 📄 许可

本项目仅供学习交流使用，请勿用于商业用途。使用时请遵守相关法律法规和平台规则。
