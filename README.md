# QQ自动回赞脚本 v3.0

基于 AutoJS6 的QQ自动回赞脚本，支持配置文件外部化管理，保护敏感信息安全。

## ✨ 特性

- 🔧 **配置外部化**: 敏感信息独立存储，支持版本控制
- 🛡️ **安全保护**: 配置文件自动加入git忽略列表
- 📱 **智能适配**: 使用相对坐标，适配不同屏幕尺寸
- 🔄 **自动重试**: 内置重试机制，提高执行成功率
- 📊 **详细日志**: 完整的执行日志和统计信息
- ⚙️ **高度可配**: 所有参数均可通过配置文件调整

## 📁 文件结构

```
├── QQ自动回赞3.0.js      # 主脚本文件
├── config.def.json       # 配置模板（安全，可提交）
├── config.json          # 实际配置（敏感，已忽略）
├── .gitignore           # Git忽略规则
├── 配置文件说明.md       # 配置详细说明
└── README.md            # 项目说明
```

## 🚀 快速开始

### 1. 环境准备
- Android 7.0+ 系统
- AutoJS6 最新版本
- 已开启无障碍服务权限

### 2. 首次运行
```bash
# 1. 直接运行脚本
# 脚本会自动检测配置文件不存在，并创建默认配置

# 2. 脚本会提示配置QQ号，此时需要：
# - 编辑 config.json 文件
# - 修改 app.myQQ 为你的真实QQ号
# - 根据需要调整其他配置项

# 3. 重新运行脚本
```

### 3. 配置解锁手势
如果你的手机有锁屏密码，需要配置解锁手势：

1. 开启"开发者选项" → "指针位置"
2. 在锁屏界面绘制解锁图案，记录坐标
3. 编辑 `config.json` 中的 `unlock.gesturePath`

## ⚙️ 配置说明

### 核心配置
```json
{
  "app": {
    "myQQ": "你的QQ号",        // 必须修改
    "clickCounts": 25,         // 每轮点赞数量
    "loadMoreTime": 0          // 加载更多次数，0=无限制
  }
}
```

### 时间配置
```json
{
  "timing": {
    "refreshDelay": 2000,      // 刷新等待时间
    "scrollDelay": 1000,       // 滑动等待时间
    "waitTimeout": 5000,       // 元素等待超时
    "clickDelay": 50           // 点击间隔
  }
}
```

详细配置说明请查看 [配置文件说明.md](配置文件说明.md)

## 🔒 安全特性

### 敏感信息保护
- ✅ `config.json` 自动加入 `.gitignore`
- ✅ 只有配置模板 `config.def.json` 会被提交
- ✅ 解锁密码轨迹等敏感信息完全本地化

### 版本控制安全
```bash
# 这些文件会被提交到版本控制
✅ QQ自动回赞3.0.js
✅ config.def.json
✅ .gitignore
✅ README.md

# 这些文件会被忽略（包含敏感信息）
❌ config.json
❌ *.log
❌ personal_*.json
```

## 🛠️ 使用指南

### 基本使用
1. 确保QQ已登录
2. 运行脚本
3. 脚本会自动解锁屏幕（如需要）
4. 进入QQ点赞页面
5. 自动执行点赞操作

### 高级配置
- **调试模式**: 设置 `debug.enableDetailedLog: true`
- **性能优化**: 调整各种延迟时间
- **坐标适配**: 使用相对坐标比例

### 故障排除
1. **配置文件错误**: 检查JSON格式
2. **解锁失败**: 重新配置解锁手势坐标
3. **点赞无效**: 确认QQ号正确，检查权限

## 📊 功能特性

### 智能检测
- 自动检测屏幕状态
- 智能判断是否需要继续点赞
- 自动处理"显示更多"按钮

### 错误处理
- 完善的异常捕获机制
- 自动重试失败操作
- 详细的错误日志输出

### 统计功能
- 实时显示点赞进度
- 统计总点赞次数和轮数
- 记录执行时间

## 🔄 更新日志

### v3.0 (当前版本)
- ✨ 配置文件外部化
- 🔒 敏感信息保护
- 📱 相对坐标适配
- 🛡️ 完善错误处理
- 📊 详细日志统计

### v2.0 (原版本)
- 基础点赞功能
- 硬编码配置
- 简单错误处理

## ⚠️ 注意事项

1. **合规使用**: 请遵守QQ使用条款，适度使用
2. **隐私保护**: 不要分享包含敏感信息的配置文件
3. **备份重要**: 定期备份配置文件（注意敏感信息）
4. **版本兼容**: 确保使用AutoJS6最新版本

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改（不要包含 `config.json`）
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目仅供学习交流使用，请勿用于商业用途。

## 🆘 支持

如果遇到问题：
1. 查看 [配置文件说明.md](配置文件说明.md)
2. 检查控制台日志输出
3. 确认配置文件格式正确
4. 验证无障碍服务权限
