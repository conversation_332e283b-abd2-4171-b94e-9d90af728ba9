/**
 * QQ自动回赞脚本 - AutoJS6版本
 * 功能：自动为QQ好友点赞
 * 版本：3.0
 * 适配：AutoJS6
 *
 * 主要改进：
 * 1. 使用现代ES6+语法和AutoJS6规范
 * 2. 改进错误处理和日志输出
 * 3. 使用更精确的选择器和等待机制
 * 4. 优化手势操作和坐标计算
 * 5. 增加配置参数和模块化设计
 * 6. 配置文件外部化，支持敏感信息分离
 */

"use strict";

// 等待无障碍服务启动
auto.waitFor();
console.log("✓ 无障碍服务已启动");

/**
 * 加载QQ配置文件
 */
function loadQQConfig() {
    var qqConfigPath = files.path("./QQConfig.json");
    var defaultQQConfigPath = files.path("./QQConfig.def.json");

    try {
        // 检查QQ配置文件是否存在
        if (!files.exists(qqConfigPath)) {
            console.warn("⚠ QQ配置文件 QQConfig.json 不存在");

            // 检查是否有默认QQ配置文件
            if (files.exists(defaultQQConfigPath)) {
                console.log("📋 发现默认QQ配置文件，正在复制...");
                files.copy(defaultQQConfigPath, qqConfigPath);
                console.log("✓ 已创建QQ配置文件，请修改 QQConfig.json 中的参数后重新运行");
                toast("请先配置 QQConfig.json 文件");
                exit();
            } else {
                throw new Error("QQ配置文件和默认QQ配置文件都不存在");
            }
        }

        // 读取QQ配置文件
        var qqConfigContent = files.read(qqConfigPath);
        var qqConfig = JSON.parse(qqConfigContent);

        // 验证必要的配置项
        if (!qqConfig.app || !qqConfig.app.myQQ || qqConfig.app.myQQ === "请填入你的QQ号") {
            throw new Error("请在 QQConfig.json 中配置正确的QQ号");
        }

        console.log("✓ QQ配置文件加载成功");
        return qqConfig;

    } catch (error) {
        console.error("❌ 加载QQ配置文件失败:", error.message);
        toast("QQ配置文件加载失败: " + error.message);
        exit();
    }
}

// 加载QQ配置
var CONFIG = loadQQConfig();

/**
 * 解锁屏幕
 */
function unlockScreen() {
    console.log("检查屏幕状态...");

    if (!device.isScreenOn()) {
        console.log("屏幕已关闭，开始解锁...");

        // 点亮屏幕
        device.wakeUp();
        sleep(1000);

        // 使用配置文件中的相对坐标滑动到密码输入界面
        var centerX = device.width / 2;
        var startY = device.height * CONFIG.coordinates.unlock.swipeStartYRatio;
        var endY = device.height * CONFIG.coordinates.unlock.swipeEndYRatio;

        swipe(centerX, startY, centerX, endY, 1000);
        sleep(1000);

        // 根据配置的解锁类型执行相应的解锁操作
        switch (CONFIG.unlock.type) {
            case 'none':
                console.log("✓ 无密码解锁，仅上滑完成");
                break;

            case 'gesture':
                console.log("执行手势密码解锁...");
                executeGestureUnlock();
                break;

            case 'pin':
                console.log("执行数字密码解锁...");
                executePinUnlock();
                break;

            default:
                console.warn("⚠ 未知的解锁类型，跳过密码输入");
                break;
        }

        sleep(1000);
        console.log("✓ 屏幕解锁完成");
    } else {
        console.log("✓ 屏幕已点亮");
    }
}

/**
 * 执行手势密码解锁
 */
function executeGestureUnlock() {
    try {
        var gesturePath = CONFIG.unlock.gesturePath.map(function(point) {
            return [point.x, point.y];
        });

        // 使用apply方法展开参数数组
        var args = [CONFIG.unlock.unlockGestureDuration].concat(gesturePath);
        gesture.apply(null, args);

        console.log("✓ 手势密码输入完成");
    } catch (error) {
        console.error("❌ 手势密码输入失败:", error.message);
    }
}

/**
 * 执行数字密码解锁
 */
function executePinUnlock() {
    try {
        var password = CONFIG.unlock.pinPassword;
        var keypad = CONFIG.unlock.pinKeypad.layout;

        // 创建数字到坐标的映射
        var numberToCoords = {};
        keypad.forEach(function(row) {
            row.forEach(function(key) {
                numberToCoords[key.number] = { x: key.x, y: key.y };
            });
        });

        console.log("输入数字密码: " + password.replace(/./g, '*'));

        // 逐个点击密码数字
        for (var i = 0; i < password.length; i++) {
            var digit = parseInt(password[i]);
            var coords = numberToCoords[digit];

            if (coords) {
                click(coords.x, coords.y);
                console.log("✓ 点击数字 " + digit);
                sleep(200); // 短暂延迟避免输入过快
            } else {
                console.error("❌ 找不到数字 " + digit + " 的坐标");
            }
        }

        console.log("✓ 数字密码输入完成");
    } catch (error) {
        console.error("❌ 数字密码输入失败:", error.message);
    }
}

/**
 * 进入QQ点赞页面
 */
function enterLikePage() {
    console.log("正在进入QQ点赞页面...");

    try {
        // 启动QQ并进入个人资料页
        app.startActivity({
            data: "mqqapi://card/show_pslcard?&uin=" + CONFIG.app.myQQ
        });

        // 等待页面加载并点击赞数按钮
        console.log("等待赞数按钮出现...");
        var likeButton = descEndsWith('次赞').findOne(CONFIG.timing.waitTimeout);

        if (likeButton) {
            likeButton.click();
            console.log("✓ 已点击赞数按钮");
        } else {
            throw new Error("未找到赞数按钮，请检查QQ号是否正确");
        }

        // 等待访客页面加载
        console.log("等待访客页面加载...");
        var activityLoaded = waitForActivity('com.tencent.mobileqq.activity.VisitorsActivity', CONFIG.timing.waitTimeout);

        if (!activityLoaded) {
            console.warn("⚠ 访客页面加载超时，继续执行...");
        }

        sleep(500);

        // 下拉刷新页面（使用配置文件中的坐标比例）
        console.log("正在刷新页面...");
        var refreshStartY = device.height * CONFIG.coordinates.refresh.startYRatio;
        var refreshEndY = device.height * CONFIG.coordinates.refresh.endYRatio;
        gesture(350, [device.width / 2, refreshStartY], [device.width / 2, refreshEndY]);
        sleep(CONFIG.timing.refreshDelay);

        console.log("✓ 成功进入点赞页面");
        return true;

    } catch (error) {
        console.error("❌ 进入点赞页面失败:", error.message);
        return false;
    }
}

/**
 * 执行点赞操作
 */
function performLiking() {
    console.log("开始执行点赞操作...");

    var isRunning = true;
    var currentLoadMoreTime = CONFIG.app.loadMoreTime;
    var totalLikes = 0;
    var roundCount = 0;

    while (isRunning) {
        try {
            roundCount++;
            if (CONFIG.debug.enableDetailedLog) {
                console.log(`--- 第 ${roundCount} 轮点赞 ---`);
            }

            // 等待页面稳定
            sleep(500);

            // 查找所有赞按钮
            var likeButtons = desc('赞').find();
            var likeCount = Math.min(likeButtons.length, CONFIG.app.clickCounts);

            if (CONFIG.debug.enableDetailedLog) {
                console.log("找到 " + likeButtons.length + " 个赞按钮，准备点击 " + likeCount + " 个");
            }

            if (likeButtons.length === 0) {
                console.log("未找到赞按钮，可能已到达列表底部");
                isRunning = false;
                break;
            }

            // 批量点赞
            var successCount = 0;
            for (var i = 0; i < likeCount; i++) {
                try {
                    var button = likeButtons[i];
                    if (button && button.clickable()) {
                        var clickResult = button.click();
                        if (clickResult) {
                            successCount++;
                            totalLikes++;
                        }
                        if (CONFIG.debug.logClickResults && !clickResult) {
                            console.warn(`第 ${i + 1} 个赞按钮点击失败`);
                        }
                    }
                    // 使用配置文件中的点击延迟
                    sleep(CONFIG.timing.clickDelay);
                } catch (e) {
                    if (CONFIG.debug.enableDetailedLog) {
                        console.warn(`点击第 ${i + 1} 个赞按钮失败:`, e.message);
                    }
                }
            }

            if (CONFIG.debug.showStatistics) {
                console.log(`✓ 本轮成功点赞 ${successCount} 次，累计点赞 ${totalLikes} 次`);
            }
            sleep(CONFIG.timing.scrollDelay);

            // 向上滑动加载更多内容（使用配置文件中的坐标比例）
            console.log("正在滑动加载更多内容...");
            var scrollStartY = device.height * CONFIG.coordinates.scroll.startYRatio;
            var scrollEndY = device.height * CONFIG.coordinates.scroll.endYRatio;
            gesture(350, [device.width / 2, scrollStartY], [device.width / 2, scrollEndY]);
            sleep(CONFIG.timing.scrollDelay);

            // 检查是否需要继续
            isRunning = shouldContinueLiking(currentLoadMoreTime);

            if (isRunning && currentLoadMoreTime > 0) {
                currentLoadMoreTime--;
            }

        } catch (error) {
            console.error("❌ 点赞过程中出现错误:", error.message);
            isRunning = false;
        }
    }

    console.log(`🎉 点赞操作完成！总共进行 ${roundCount} 轮，累计点赞 ${totalLikes} 次`);
    toast(`点赞完毕！共点赞 ${totalLikes} 次`);
}

/**
 * 判断是否应该继续点赞
 */
function shouldContinueLiking(loadMoreTime) {
    try {
        // 检查是否有时间标记（表示还有更多内容）
        var hasTimeMarkers = textMatches(/.*\:.*/).exists() || text('昨天').exists();

        if (hasTimeMarkers) {
            // 检查是否有"显示更多"按钮
            var showMoreButton = className('android.widget.TextView').text('显示更多').findOnce();

            if (showMoreButton) {
                if (loadMoreTime > 0) {
                    console.log("点击显示更多按钮...");
                    showMoreButton.click();
                    sleep(2000); // 等待加载
                    return true;
                } else if (CONFIG.app.loadMoreTime === 0) {
                    // 如果配置为0，表示不限制加载更多次数
                    console.log("点击显示更多按钮...");
                    showMoreButton.click();
                    sleep(2000);
                    return true;
                } else {
                    console.log("已达到加载更多次数限制，停止点赞");
                    return false;
                }
            } else {
                if (CONFIG.debug.enableDetailedLog) {
                    console.log("继续点赞...");
                }
                return true;
            }
        } else {
            console.log("未找到更多内容标记，停止点赞");
            return false;
        }
    } catch (error) {
        console.error("❌ 检查继续条件时出错:", error.message);
        return false;
    }
}

/**
 * 主函数
 */
function main() {
    console.log("=== QQ自动回赞脚本启动 ===");
    console.log("启动时间: " + new Date().toLocaleString());
    console.log("配置信息: QQ号=" + CONFIG.app.myQQ + ", 点赞数=" + CONFIG.app.clickCounts + ", 加载更多=" + CONFIG.app.loadMoreTime + "次");

    try {
        // 解锁屏幕
        unlockScreen();

        // 进入点赞页面
        if (!enterLikePage()) {
            console.error("❌ 无法进入点赞页面，脚本退出");
            toast("无法进入点赞页面");
            return;
        }

        // 执行点赞
        performLiking();

        console.log("✅ 脚本执行完成");
        console.log("结束时间: " + new Date().toLocaleString());

    } catch (error) {
        console.error("❌ 脚本执行失败:", error.message);
        toast("脚本执行失败: " + error.message);
    }
}

// 启动脚本
main();
