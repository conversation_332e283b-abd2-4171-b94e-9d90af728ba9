/**
 * QQ脚本解锁配置助手
 * 功能：帮助用户配置不同类型的解锁方式
 * 版本：1.0
 * 适配：AutoJS6
 */

"use strict";

// 等待无障碍服务启动
auto.waitFor();
console.log("✓ 无障碍服务已启动");

/**
 * 显示主菜单
 */
function showMainMenu() {
    const choice = dialogs.select("选择解锁配置类型", [
        "无密码解锁（仅上滑）",
        "手势密码解锁",
        "数字密码解锁",
        "测试当前配置",
        "退出"
    ]);
    
    switch (choice) {
        case 0:
            configureNoneUnlock();
            break;
        case 1:
            configureGestureUnlock();
            break;
        case 2:
            configurePinUnlock();
            break;
        case 3:
            testCurrentConfig();
            break;
        case 4:
            exit();
            break;
        default:
            exit();
    }
}

/**
 * 配置无密码解锁
 */
function configureNoneUnlock() {
    const config = {
        type: "none"
    };
    
    dialogs.alert("无密码解锁配置", "已配置为无密码解锁模式\n仅执行点亮屏幕和上滑操作");
    
    updateUnlockConfig(config);
    showMainMenu();
}

/**
 * 配置手势密码解锁
 */
function configureGestureUnlock() {
    dialogs.alert("手势密码配置", 
        "请按以下步骤操作：\n" +
        "1. 开启开发者选项中的'指针位置'\n" +
        "2. 锁定屏幕\n" +
        "3. 点击确定后，在锁屏界面绘制解锁图案\n" +
        "4. 记录每个关键点的坐标"
    );
    
    const gesturePoints = [];
    let continueAdding = true;
    
    while (continueAdding) {
        const x = dialogs.rawInput("输入坐标", `请输入第${gesturePoints.length + 1}个点的X坐标：`);
        if (x === null) break;
        
        const y = dialogs.rawInput("输入坐标", `请输入第${gesturePoints.length + 1}个点的Y坐标：`);
        if (y === null) break;
        
        const comment = dialogs.rawInput("添加说明", "为这个点添加说明（可选）：") || `第${gesturePoints.length + 1}个点`;
        
        gesturePoints.push({
            x: parseInt(x),
            y: parseInt(y),
            comment: comment
        });
        
        continueAdding = dialogs.confirm("继续添加", "是否继续添加下一个点？");
    }
    
    if (gesturePoints.length > 0) {
        const duration = dialogs.rawInput("手势时长", "请输入手势执行时长（毫秒，建议1000）：") || "1000";
        
        const config = {
            type: "gesture",
            unlockGestureDuration: parseInt(duration),
            gesturePath: gesturePoints
        };
        
        updateUnlockConfig(config);
        dialogs.alert("配置完成", `已配置${gesturePoints.length}个手势点`);
    }
    
    showMainMenu();
}

/**
 * 配置数字密码解锁
 */
function configurePinUnlock() {
    const password = dialogs.rawInput("数字密码", "请输入你的数字密码：");
    if (!password) {
        showMainMenu();
        return;
    }
    
    dialogs.alert("键盘坐标配置", 
        "请按以下步骤操作：\n" +
        "1. 开启开发者选项中的'指针位置'\n" +
        "2. 锁定屏幕并进入数字密码界面\n" +
        "3. 记录0-9每个数字按键的坐标"
    );
    
    const keypad = [
        [{number: 1}, {number: 2}, {number: 3}],
        [{number: 4}, {number: 5}, {number: 6}],
        [{number: 7}, {number: 8}, {number: 9}],
        [{number: 0}]
    ];
    
    // 配置每个数字的坐标
    for (let row of keypad) {
        for (let key of row) {
            const x = dialogs.rawInput("键盘坐标", `请输入数字 ${key.number} 的X坐标：`);
            if (x === null) {
                showMainMenu();
                return;
            }
            
            const y = dialogs.rawInput("键盘坐标", `请输入数字 ${key.number} 的Y坐标：`);
            if (y === null) {
                showMainMenu();
                return;
            }
            
            key.x = parseInt(x);
            key.y = parseInt(y);
        }
    }
    
    const config = {
        type: "pin",
        pinPassword: password,
        pinKeypad: {
            comment: "数字键盘布局 (3x4网格)",
            layout: keypad
        }
    };
    
    updateUnlockConfig(config);
    dialogs.alert("配置完成", "数字密码解锁配置已保存");
    showMainMenu();
}

/**
 * 测试当前配置
 */
function testCurrentConfig() {
    try {
        const configPath = files.path("./QQConfig.json");
        if (!files.exists(configPath)) {
            dialogs.alert("错误", "QQConfig.json 文件不存在");
            showMainMenu();
            return;
        }
        
        const configContent = files.read(configPath);
        const config = JSON.parse(configContent);
        
        if (!config.unlock) {
            dialogs.alert("错误", "配置文件中没有解锁配置");
            showMainMenu();
            return;
        }
        
        const unlockType = config.unlock.type || "未配置";
        let configInfo = `解锁类型: ${unlockType}\n`;
        
        switch (unlockType) {
            case "none":
                configInfo += "配置：无密码解锁";
                break;
            case "gesture":
                const gestureCount = config.unlock.gesturePath ? config.unlock.gesturePath.length : 0;
                configInfo += `配置：${gestureCount}个手势点\n`;
                configInfo += `时长：${config.unlock.unlockGestureDuration}毫秒`;
                break;
            case "pin":
                const passwordLength = config.unlock.pinPassword ? config.unlock.pinPassword.length : 0;
                configInfo += `密码长度：${passwordLength}位\n`;
                configInfo += `键盘配置：${config.unlock.pinKeypad ? '已配置' : '未配置'}`;
                break;
            default:
                configInfo += "配置：未知类型";
        }
        
        dialogs.alert("当前配置", configInfo);
        
    } catch (error) {
        dialogs.alert("错误", "读取配置文件失败：" + error.message);
    }
    
    showMainMenu();
}

/**
 * 更新解锁配置
 */
function updateUnlockConfig(unlockConfig) {
    try {
        const configPath = files.path("./QQConfig.json");
        let config = {};
        
        // 读取现有配置
        if (files.exists(configPath)) {
            const configContent = files.read(configPath);
            config = JSON.parse(configContent);
        }
        
        // 更新解锁配置
        config.unlock = unlockConfig;
        
        // 保存配置
        files.write(configPath, JSON.stringify(config, null, 2));
        console.log("✓ 解锁配置已更新");
        
    } catch (error) {
        dialogs.alert("错误", "保存配置失败：" + error.message);
    }
}

// 启动配置助手
console.log("=== QQ脚本解锁配置助手 ===");
showMainMenu();
