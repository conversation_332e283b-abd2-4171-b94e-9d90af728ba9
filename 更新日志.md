# 更新日志

## v3.1 (2024-08-11)

### 🔄 配置文件重命名
为了避免将来可能的配置文件命名冲突，将所有配置文件重命名为更具体的名称：

#### 文件重命名
- `config.json` → `QQConfig.json`
- `config.def.json` → `QQConfig.def.json`

#### 代码更新
- 更新主脚本中的配置文件加载函数：`loadConfig()` → `loadQQConfig()`
- 更新所有配置文件路径引用
- 更新错误提示信息中的文件名

#### 文档更新
- 更新 `README.md` 中的文件结构说明
- 更新 `配置文件说明.md` 中的所有文件名引用
- 更新快速开始指南中的配置步骤

#### Git忽略规则更新
- 更新 `.gitignore` 文件，添加 `QQConfig.json` 到忽略列表
- 保留通用的 `*Config.json` 和 `config.json` 规则以兼容其他配置文件

### 🎯 改进目标
1. **命名空间隔离**: 为QQ脚本配置使用专用的命名空间
2. **扩展性**: 为将来可能的其他脚本配置文件预留空间
3. **清晰性**: 配置文件名称更加明确，便于管理

### 📋 迁移指南

#### 对于现有用户
如果你已经有 `config.json` 文件，请按以下步骤迁移：

1. **备份现有配置**:
   ```bash
   cp config.json config_backup.json
   ```

2. **重命名配置文件**:
   ```bash
   mv config.json QQConfig.json
   ```

3. **验证配置**:
   - 运行脚本确认配置文件正确加载
   - 检查所有配置项是否正常工作

#### 对于新用户
- 直接运行脚本，会自动创建 `QQConfig.json` 配置文件
- 按照提示修改配置即可

### 🔧 技术细节

#### 函数变更
```javascript
// 旧版本
function loadConfig() {
    const configPath = files.path("./config.json");
    const defaultConfigPath = files.path("./config.def.json");
    // ...
}

// 新版本
function loadQQConfig() {
    const qqConfigPath = files.path("./QQConfig.json");
    const defaultQQConfigPath = files.path("./QQConfig.def.json");
    // ...
}
```

#### 错误信息更新
- 所有错误提示中的文件名都已更新为新的命名
- 保持了错误信息的清晰性和准确性

### 🛡️ 安全性
- 新的配置文件同样受到 `.gitignore` 保护
- 敏感信息仍然不会被提交到版本控制系统
- 增加了通用的配置文件忽略规则，提高安全性

### 📈 兼容性
- 脚本会自动检测并创建新的配置文件
- 保持了所有原有功能的完整性
- 配置文件结构保持不变，只是文件名更改

## v3.0 (2024-08-11)

### ✨ 主要特性
- 配置文件外部化
- 敏感信息保护
- 相对坐标适配
- 完善错误处理
- 详细日志统计

### 🔧 技术改进
- 使用现代ES6+语法和AutoJS6规范
- 改进错误处理和日志输出
- 使用更精确的选择器和等待机制
- 优化手势操作和坐标计算
- 增加配置参数和模块化设计

## v2.0 (原版本)

### 基础功能
- QQ自动回赞功能
- 基本的屏幕解锁
- 简单的点赞循环逻辑
- 硬编码配置参数

### 局限性
- 配置参数硬编码在脚本中
- 缺乏错误处理机制
- 使用绝对坐标，兼容性差
- 缺乏详细的日志输出
