# 解锁方式更新说明

## 📱 新增功能

### 支持三种解锁方式

之前版本只支持手势密码解锁，现在支持三种不同的解锁方式：

1. **无密码解锁** (`type: "none"`)
   - 仅点亮屏幕后上滑
   - 适用于无锁屏密码的设备

2. **手势密码解锁** (`type: "gesture"`)
   - 点亮屏幕 → 上滑 → 绘制手势图案
   - 原有功能，保持兼容

3. **数字密码解锁** (`type: "pin"`)
   - 点亮屏幕 → 上滑 → 点击数字键盘
   - 新增功能，支持PIN码解锁

## 🔧 配置变更

### 配置文件结构更新

#### 新增字段
```json
{
  "unlock": {
    "type": "gesture",              // 新增：解锁类型
    "pinPassword": "123456",        // 新增：数字密码
    "pinKeypad": {                  // 新增：键盘布局
      "layout": [...]
    }
  }
}
```

#### 兼容性
- 现有的 `gesturePath` 和 `unlockGestureDuration` 配置保持不变
- 如果没有配置 `type` 字段，默认使用 `gesture` 类型
- 向后兼容，现有配置无需修改

## 🛠️ 使用指南

### 快速配置

#### 方法1：使用配置助手（推荐）
```bash
# 运行配置助手
node 解锁配置助手.js
```

配置助手提供图形界面，支持：
- 选择解锁类型
- 引导式坐标录入
- 配置验证测试
- 一键保存配置

#### 方法2：手动编辑配置文件

##### 无密码解锁
```json
{
  "unlock": {
    "type": "none"
  }
}
```

##### 手势密码解锁
```json
{
  "unlock": {
    "type": "gesture",
    "unlockGestureDuration": 1000,
    "gesturePath": [
      {"x": 250, "y": 2100, "comment": "起始点"},
      {"x": 550, "y": 1520, "comment": "结束点"}
    ]
  }
}
```

##### 数字密码解锁
```json
{
  "unlock": {
    "type": "pin",
    "pinPassword": "123456",
    "pinKeypad": {
      "layout": [
        [{"number": 1, "x": 200, "y": 1200}, {"number": 2, "x": 400, "y": 1200}, {"number": 3, "x": 600, "y": 1200}],
        [{"number": 4, "x": 200, "y": 1400}, {"number": 5, "x": 400, "y": 1400}, {"number": 6, "x": 600, "y": 1400}],
        [{"number": 7, "x": 200, "y": 1600}, {"number": 8, "x": 400, "y": 1600}, {"number": 9, "x": 600, "y": 1600}],
        [{"number": 0, "x": 400, "y": 1800}]
      ]
    }
  }
}
```

## 🔍 坐标获取方法

### 通用步骤
1. 开启"开发者选项"
2. 启用"指针位置"
3. 锁定屏幕
4. 进入相应的解锁界面
5. 记录坐标

### 手势密码坐标
- 在手势解锁界面绘制图案
- 记录每个关键转折点的坐标
- 按绘制顺序排列坐标

### 数字密码坐标
- 在数字密码界面点击每个数字
- 记录0-9所有数字的坐标
- 按标准键盘布局排列

## 🚨 注意事项

### 安全提醒
- 数字密码会以明文形式存储在配置文件中
- 确保 `QQConfig.json` 不会被分享或提交到版本控制
- 建议定期更改密码并更新配置

### 兼容性说明
- 新版本完全兼容旧配置
- 如果不配置 `type` 字段，默认使用手势解锁
- 建议更新配置文件以明确指定解锁类型

### 故障排除
1. **解锁失败**：检查解锁类型配置是否正确
2. **坐标错误**：重新获取坐标，注意屏幕分辨率变化
3. **密码错误**：验证数字密码是否正确
4. **键盘布局**：确保0-9所有数字都有对应坐标

## 📈 性能优化

### 解锁速度
- 无密码解锁：最快，仅需上滑
- 手势密码：中等，取决于图案复杂度
- 数字密码：较慢，需要逐个点击数字

### 建议配置
- 简单环境：使用无密码解锁
- 安全要求高：使用手势或数字密码
- 追求速度：优化坐标和延迟时间

## 🔄 迁移指南

### 从旧版本升级
1. 备份现有 `QQConfig.json`
2. 在 `unlock` 配置中添加 `"type": "gesture"`
3. 测试解锁功能是否正常
4. 根据需要切换到其他解锁类型

### 配置验证
```javascript
// 验证配置是否正确
node 解锁配置助手.js
// 选择"测试当前配置"选项
```

## 📞 技术支持

如果遇到问题：
1. 查看详细的配置文件说明
2. 使用配置助手进行配置
3. 检查控制台日志输出
4. 验证坐标获取方法是否正确
