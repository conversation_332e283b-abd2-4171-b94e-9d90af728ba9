# QQ配置文件说明

## 文件结构

### QQConfig.def.json (QQ默认配置模板)
- 这是QQ脚本配置文件的模板，包含所有配置项的说明
- 不包含敏感信息，可以安全地提交到版本控制系统
- 当 `QQConfig.json` 不存在时，脚本会自动复制此文件创建配置文件

### QQConfig.json (QQ实际配置文件)
- 包含真实的QQ配置信息，如QQ号、解锁密码轨迹等敏感信息
- **已添加到 .gitignore 中，不会被提交到版本控制系统**
- 需要用户根据自己的实际情况进行配置

## 配置项详解

### app 应用配置
```json
{
  "app": {
    "myQQ": "你的QQ号",           // 必填：目标QQ号
    "clickCounts": 25,            // 每轮滑动时的点赞次数
    "loadMoreTime": 0,            // 点击"显示更多"的次数限制，0表示不限制
    "maxRetries": 3               // 操作失败时的最大重试次数
  }
}
```

### timing 时间配置
```json
{
  "timing": {
    "refreshDelay": 2000,         // 刷新页面后的等待时间（毫秒）
    "scrollDelay": 1000,          // 滑动操作后的等待时间（毫秒）
    "waitTimeout": 5000,          // 等待元素出现的超时时间（毫秒）
    "clickDelay": 50              // 点击操作之间的延迟（毫秒）
  }
}
```

### unlock 解锁配置
```json
{
  "unlock": {
    "unlockGestureDuration": 1000,  // 解锁手势的执行时长（毫秒）
    "gesturePath": [                // 解锁手势路径
      {
        "x": 250,
        "y": 2100,
        "comment": "解锁起始点"
      },
      {
        "x": 250,
        "y": 1800,
        "comment": "第二个点"
      }
      // ... 更多点
    ]
  }
}
```

### coordinates 坐标配置
```json
{
  "coordinates": {
    "refresh": {
      "startYRatio": 0.3,         // 刷新手势起始Y坐标比例
      "endYRatio": 0.7            // 刷新手势结束Y坐标比例
    },
    "scroll": {
      "startYRatio": 0.8,         // 滚动手势起始Y坐标比例
      "endYRatio": 0.2            // 滚动手势结束Y坐标比例
    },
    "unlock": {
      "swipeStartYRatio": 0.8,    // 解锁滑动起始Y坐标比例
      "swipeEndYRatio": 0.1       // 解锁滑动结束Y坐标比例
    }
  }
}
```

### debug 调试配置
```json
{
  "debug": {
    "enableDetailedLog": true,    // 是否启用详细日志
    "logClickResults": true,      // 是否记录点击结果
    "showStatistics": true        // 是否显示统计信息
  }
}
```

## 首次使用步骤

1. **运行脚本**：首次运行时，脚本会检测到 `QQConfig.json` 不存在
2. **自动创建配置**：脚本会自动从 `QQConfig.def.json` 复制创建 `QQConfig.json`
3. **修改配置**：编辑 `QQConfig.json` 文件，至少需要修改以下项目：
   - `app.myQQ`: 改为你的真实QQ号
   - `unlock.gesturePath`: 根据你的解锁图案调整坐标
4. **重新运行**：保存配置文件后重新运行脚本

## 解锁手势配置

### 获取解锁坐标
1. 开启开发者选项中的"指针位置"
2. 在锁屏界面绘制解锁图案
3. 记录每个关键点的坐标
4. 将坐标填入 `unlock.gesturePath` 数组

### 坐标格式
```json
{
  "x": 横坐标,
  "y": 纵坐标,
  "comment": "说明文字（可选）"
}
```

## 安全注意事项

### 敏感信息保护
- `QQConfig.json` 包含敏感信息，已加入 `.gitignore`
- 不要将 `QQConfig.json` 分享给他人
- 备份时注意保护敏感信息

### 版本控制
- 只提交 `QQConfig.def.json` 到版本控制系统
- `QQConfig.json` 会被 git 自动忽略
- 团队协作时，每个人需要创建自己的 `QQConfig.json`

## 故障排除

### 配置文件加载失败
- 检查 JSON 格式是否正确
- 确保所有必需的配置项都存在
- 查看控制台错误信息

### 解锁失败
- 重新获取解锁图案坐标
- 调整 `unlockGestureDuration` 时长
- 检查坐标是否适配当前屏幕分辨率

### 点赞无效
- 确认QQ号配置正确
- 检查无障碍服务权限
- 调整点击延迟时间

## 配置优化建议

### 性能优化
- 适当增加 `clickDelay` 避免操作过快
- 根据网络情况调整 `waitTimeout`
- 关闭不需要的调试日志

### 稳定性提升
- 设置合理的 `maxRetries` 重试次数
- 调整滑动和刷新的延迟时间
- 使用相对坐标比例而非绝对坐标
