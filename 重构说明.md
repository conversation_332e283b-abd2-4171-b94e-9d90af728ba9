# QQ回赞脚本重构说明

## 概述
将原有的QQ回赞2.0.js脚本按照AutoJS6规范进行了全面重构，提升了代码质量、可维护性和稳定性。

## 主要改进

### 1. 代码结构优化
- **模块化设计**: 将功能拆分为独立的函数模块
- **配置集中管理**: 使用CONFIG对象统一管理所有配置参数
- **严格模式**: 添加`"use strict"`启用严格模式
- **JSDoc注释**: 为所有函数添加详细的文档注释

### 2. AutoJS6规范适配
- **现代选择器语法**: 使用AutoJS6推荐的选择器方法
- **改进的等待机制**: 使用`findOne(timeout)`替代无限等待
- **错误处理**: 添加try-catch块和详细的错误信息
- **日志系统**: 使用console.log替代简单的toast提示

### 3. 坐标和手势优化
- **相对坐标**: 使用`device.width`和`device.height`计算相对坐标
- **手势参数优化**: 改进gesture和swipe函数的参数
- **延迟时间配置化**: 将所有延迟时间提取为可配置参数

### 4. 功能增强
- **重试机制**: 添加操作失败时的重试逻辑
- **统计功能**: 记录点赞次数和轮数统计
- **状态检查**: 改进页面状态和按钮存在性检查
- **超时处理**: 为所有等待操作添加超时机制

## 具体变更对比

### 原版本问题
```javascript
// 硬编码坐标
swipe(520, 1560, 520, 100, 1000)

// 无限等待
descEndsWith('次赞').findOne().click()

// 简单循环，缺乏错误处理
while (start) {
    for (let i = 0; i < clickCounts; i++) {
        desc('赞').find().click()
    }
}
```

### 重构后改进
```javascript
// 相对坐标计算
const centerX = device.width / 2;
const startY = device.height * 0.8;
const endY = device.height * 0.1;
swipe(centerX, startY, centerX, endY, 1000);

// 带超时的等待
const likeButton = descEndsWith('次赞').findOne(CONFIG.waitTimeout);
if (likeButton) {
    likeButton.click();
} else {
    throw new Error("未找到赞数按钮");
}

// 完善的错误处理和统计
while (isRunning) {
    try {
        const likeButtons = desc('赞').find();
        for (let i = 0; i < likeCount; i++) {
            if (likeButtons[i] && likeButtons[i].clickable()) {
                if (likeButtons[i].click()) {
                    totalLikes++;
                }
            }
        }
    } catch (error) {
        console.error("❌ 点赞过程中出现错误:", error.message);
        isRunning = false;
    }
}
```

## 配置参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| myQQ | '747215879' | 目标QQ号，需要修改为实际QQ号 |
| clickCounts | 25 | 每轮滑动时的点赞次数 |
| loadMoreTime | 0 | 点击"显示更多"的次数限制，0表示不限制 |
| refreshDelay | 2000 | 刷新页面后的等待时间（毫秒） |
| scrollDelay | 1000 | 滑动操作后的等待时间（毫秒） |
| unlockGestureDuration | 1000 | 解锁手势的执行时长（毫秒） |
| maxRetries | 3 | 操作失败时的最大重试次数 |
| waitTimeout | 5000 | 等待元素出现的超时时间（毫秒） |

## 使用说明

### 1. 环境要求
- AutoJS6 最新版本
- Android 7.0+ 系统
- 已开启无障碍服务权限

### 2. 配置修改
在使用前请修改以下配置：
```javascript
const CONFIG = {
    myQQ: '你的QQ号',  // 必须修改
    // 其他参数可根据需要调整
};
```

### 3. 解锁手势调整
如果你的手机解锁图案不同，请修改`unlockScreen()`函数中的手势坐标：
```javascript
gesture(CONFIG.unlockGestureDuration, 
    [x1, y1], [x2, y2], [x3, y3], [x4, y4]  // 根据实际图案调整
);
```

## 注意事项

1. **QQ号配置**: 必须将CONFIG.myQQ修改为你的实际QQ号
2. **解锁图案**: 需要根据你的手机解锁图案调整坐标
3. **权限设置**: 确保AutoJS6有无障碍服务权限
4. **网络环境**: 确保QQ能正常访问网络
5. **使用频率**: 建议适度使用，避免被QQ检测为异常操作

## 兼容性

- ✅ AutoJS6 6.2.0+
- ✅ Android 7.0+
- ✅ QQ 最新版本
- ❌ AutoJS 4.x (需要额外适配)

## 故障排除

### 常见问题
1. **无法找到赞数按钮**: 检查QQ号是否正确，确保QQ已登录
2. **解锁失败**: 调整解锁手势坐标，或手动解锁后运行脚本
3. **点赞无效果**: 检查无障碍服务权限，重启AutoJS6
4. **脚本卡住**: 检查网络连接，重启QQ应用

### 调试建议
1. 开启AutoJS6的日志输出功能
2. 使用布局分析工具检查控件属性
3. 逐步测试各个功能模块
4. 根据实际情况调整延迟时间

## 更新日志

### v3.0 (2024-08-11)
- 完全重构代码结构
- 适配AutoJS6规范
- 添加完善的错误处理
- 优化坐标计算和手势操作
- 增加配置参数和统计功能
