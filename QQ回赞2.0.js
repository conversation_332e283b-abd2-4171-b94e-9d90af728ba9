/**
 * QQ自动回赞脚本 - AutoJS6版本
 * 功能：自动为QQ好友点赞
 * 版本：3.0
 * 适配：AutoJS6
 */

"use strict";

// 等待无障碍服务启动
auto.waitFor();
console.log("✓ 无障碍服务已启动");

// 配置参数
const CONFIG = {
    myQQ: '747215879',           // QQ号
    clickCounts: 25,             // 每次滑动点赞次数
    loadMoreTime: 0,             // 载入更多列表次数
    refreshDelay: 2000,          // 刷新等待时间
    scrollDelay: 1000,           // 滑动等待时间
    unlockGestureDuration: 1000  // 解锁手势时长
};

/**
 * 解锁屏幕
 */
function unlockScreen() {
    console.log("检查屏幕状态...");

    if (!device.isScreenOn()) {
        console.log("屏幕已关闭，开始解锁...");

        // 点亮屏幕
        device.wakeUp();
        sleep(1000);

        // 滑动到密码输入界面
        const centerX = device.width / 2;
        const startY = device.height * 0.8;
        const endY = device.height * 0.1;

        swipe(centerX, startY, centerX, endY, 1000);
        sleep(1000);

        // 执行解锁手势（根据实际解锁图案调整）
        gesture(CONFIG.unlockGestureDuration,
            [250, 2100], [250, 1800], [550, 1520], [830, 1520]
        );
        sleep(1000);

        console.log("✓ 屏幕解锁完成");
    } else {
        console.log("✓ 屏幕已点亮");
    }
}

/**
 * 进入QQ点赞页面
 */
function enterLikePage() {
    console.log("正在进入QQ点赞页面...");

    try {
        // 启动QQ并进入个人资料页
        app.startActivity({
            data: `mqqapi://card/show_pslcard?&uin=${CONFIG.myQQ}`
        });

        // 等待页面加载并点击赞数
        const likeButton = descEndsWith('次赞').findOne(5000);
        if (likeButton) {
            likeButton.click();
            console.log("✓ 已点击赞数按钮");
        } else {
            throw new Error("未找到赞数按钮");
        }

        // 等待访客页面加载
        waitForActivity('com.tencent.mobileqq.activity.VisitorsActivity');
        sleep(500);

        // 下拉刷新页面
        console.log("正在刷新页面...");
        gesture(350, [600, 400], [600, 1000]);
        sleep(CONFIG.refreshDelay);

        console.log("✓ 成功进入点赞页面");
        return true;

    } catch (error) {
        console.error("❌ 进入点赞页面失败:", error.message);
        return false;
    }
}
/**
 * 执行点赞操作
 */
function performLiking() {
    console.log("开始执行点赞操作...");

    let isRunning = true;
    let currentLoadMoreTime = CONFIG.loadMoreTime;
    let totalLikes = 0;

    while (isRunning) {
        try {
            // 批量点赞当前页面的赞按钮
            const likeButtons = desc('赞').find();
            const likeCount = Math.min(likeButtons.length, CONFIG.clickCounts);

            console.log(`找到 ${likeButtons.length} 个赞按钮，准备点击 ${likeCount} 个`);

            for (let i = 0; i < likeCount; i++) {
                if (likeButtons[i] && likeButtons[i].clickable()) {
                    likeButtons[i].click();
                    totalLikes++;
                }
            }

            console.log(`✓ 本轮点赞完成，累计点赞 ${totalLikes} 次`);
            sleep(CONFIG.scrollDelay);

            // 向上滑动加载更多内容
            console.log("正在滑动加载更多内容...");
            gesture(350, [600, device.height * 0.9], [600, device.height * 0.1]);
            sleep(CONFIG.scrollDelay);

            // 检查是否需要继续
            isRunning = shouldContinueLiking(currentLoadMoreTime);

            if (isRunning && currentLoadMoreTime > 0) {
                currentLoadMoreTime--;
            }

        } catch (error) {
            console.error("❌ 点赞过程中出现错误:", error.message);
            isRunning = false;
        }
    }

    console.log(`🎉 点赞操作完成！总共点赞 ${totalLikes} 次`);
    toast(`点赞完毕！共点赞 ${totalLikes} 次`);
}

/**
 * 判断是否应该继续点赞
 */
function shouldContinueLiking(loadMoreTime) {
    // 检查是否有时间标记（表示还有更多内容）
    const hasTimeMarkers = textMatches(/.*\:.*/).exists() || text('昨天').exists();

    if (hasTimeMarkers) {
        // 检查是否有"显示更多"按钮
        const showMoreButton = className('android.widget.TextView').text('显示更多').findOnce();

        if (showMoreButton) {
            if (loadMoreTime > 0) {
                console.log("点击显示更多按钮...");
                showMoreButton.click();
                sleep(2000); // 等待加载
                return true;
            } else {
                console.log("已达到加载更多次数限制，停止点赞");
                return false;
            }
        } else {
            console.log("继续点赞...");
            return true;
        }
    } else {
        console.log("未找到更多内容标记，停止点赞");
        return false;
    }
}

/**
 * 主函数
 */
function main() {
    console.log("=== QQ自动回赞脚本启动 ===");
    console.log(`配置信息: QQ号=${CONFIG.myQQ}, 点赞数=${CONFIG.clickCounts}, 加载更多=${CONFIG.loadMoreTime}次`);

    try {
        // 解锁屏幕
        unlockScreen();

        // 进入点赞页面
        if (!enterLikePage()) {
            console.error("❌ 无法进入点赞页面，脚本退出");
            return;
        }

        // 执行点赞
        performLiking();

        console.log("✅ 脚本执行完成");

    } catch (error) {
        console.error("❌ 脚本执行失败:", error.message);
        toast("脚本执行失败: " + error.message);
    }
}

// 启动脚本
main();
