auto.waitFor()

// 解锁屏幕
if (!device.isScreenOn()) {
    //点亮屏幕
    device.wakeUp()
    sleep(1000)
    //滑动屏幕到输入密码界面
    swipe(520, 1560, 520, 100, 1000)
    // swipe(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.1, 600)
    sleep(1000)
    gesture(1000, [250, 2100], [250, 1800], [550, 1520], [830, 1520])
    sleep(1000)
}

// 变量设置
var loadMoreTime = 0 // 载入更多列表次数
var MyQQ = '747215879' // QQ号
var clickCounts = 25 // 点赞次数
var start = true // 点赞启动

// 进入点赞页面
app.startActivity({
    data: 'mqqapi://card/show_pslcard?&uin=' + MyQQ,
})
descEndsWith('次赞').findOne().click()
waitForActivity('com.tencent.mobileqq.activity.VisitorsActivity')
sleep(500)
// 刷新
// swipe(device.width / 2, device.height * 0.3, device.width / 2, device.height * 0.8, 500)
gestures([350, [600, 400], [600, 1000]])
sleep(2000)
// 开始点击
while (start) {
    for (let i = 0; i < clickCounts; i++) {
        desc('赞').find().click()
    }

    sleep(1 * 1000)
    // 上拉列表
    // swipe(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.1, 600)
    gestures([350, [600, 1920], [600, 400]])
    sleep(1 * 1000)
    if (textMatches(/.*\:.*/).exists() || textMatches('昨天').exists()) {
        if (loadMoreTime !== 0 && className('android.widget.TextView').text('显示更多').exists()) {
            // 载入更多列表
            while (!click('显示更多'));
            loadMoreTime -= 1
        } else if (loadMoreTime === 0 && className('android.widget.TextView').text('显示更多').exists()) {
            // 停止
            start = false
            toast('点赞完毕')
        }
    } else {
        // 停止
        start = false
        toast('点赞完毕')
    }
}

// 回到主页
// click(1140, 2870)
// click(1140, 2870)
// click(1140, 2870)
// click(740, 2870)
