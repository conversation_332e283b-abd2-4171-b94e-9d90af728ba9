# AutoJS6 项目配置说明

## project.json 配置文件

AutoJS6 项目需要一个 `project.json` 配置文件来定义项目的基本信息和构建设置。

### 配置字段说明

#### 基本信息
- `name`: 项目名称，用于显示和识别
- `versionName`: 版本名称，如 "3.1.0"
- `versionCode`: 版本号，整数值，用于版本比较
- `packageName`: 包名，用于打包APK时的应用标识
- `main`: 主入口脚本文件
- `icon`: 项目图标文件（可选）

#### 启动配置 (launchConfig)
- `displaySplash`: 是否显示启动画面
- `hideLogs`: 是否隐藏日志输出
- `stableMode`: 是否启用稳定模式
- `hiddenConsole`: 是否隐藏控制台
- `volumeUpControl`: 是否启用音量键控制

#### 构建配置 (build)
- `build_v8_cache`: 是否构建V8缓存
- `build_node_modules`: 是否构建node_modules

#### 脚本列表 (scripts)
定义项目中包含的脚本文件及其描述信息。

#### 资源文件 (assets)
需要包含在项目中的资源文件列表。

#### 忽略文件 (ignore)
构建时需要忽略的文件和目录。

## 当前项目配置

```json
{
  "name": "AutoJS6-Scripts",
  "versionName": "3.1.0", 
  "versionCode": 31,
  "packageName": "com.autojs.scripts.collection",
  "main": "QQ自动回赞3.0.js"
}
```

### 主要特性
- 🎯 以QQ自动回赞脚本为主入口
- 📱 支持多个脚本文件
- 🔧 包含配置助手工具
- 🛡️ 自动忽略敏感配置文件

## 使用说明

1. **项目识别**: AutoJS6会自动识别包含project.json的目录为项目
2. **脚本运行**: 可以直接运行项目，会执行main字段指定的脚本
3. **APK打包**: 可以将整个项目打包为独立的APK应用
4. **资源管理**: 项目内的脚本可以使用相对路径访问其他文件

## 注意事项

- project.json必须是有效的JSON格式
- main字段指定的文件必须存在
- 敏感配置文件已在ignore列表中，不会被打包
- 版本号应该随着项目更新而递增

## 故障排除

如果遇到"缺少必要的项目配置文件"错误：

1. 确认project.json文件存在于项目根目录
2. 检查JSON格式是否正确
3. 验证main字段指定的文件是否存在
4. 重启AutoJS6应用重新加载项目
